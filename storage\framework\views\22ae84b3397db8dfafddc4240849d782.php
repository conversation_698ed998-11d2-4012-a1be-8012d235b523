<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'type' => 'default',
    'url' => null,
    'skeleton' => true,
    'class' => '',
    'minHeight' => '200px',
    'loadingText' => 'Loading...',
    'errorText' => 'Failed to load content'
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'type' => 'default',
    'url' => null,
    'skeleton' => true,
    'class' => '',
    'minHeight' => '200px',
    'loadingText' => 'Loading...',
    'errorText' => 'Failed to load content'
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
    // Build classes
    $classes = collect(['lazy-content'])
        ->when($skeleton, fn($collection) => $collection->push('skeleton-loading'))
        ->when($class, fn($collection) => $collection->push($class))
        ->implode(' ');
    
    // Prepare attributes
    $attributes = collect([
        'class' => $classes,
        'data-lazy-content' => $type,
        'style' => "min-height: {$minHeight};"
    ])
    ->when($url, fn($collection) => $collection->put('data-lazy-url', $url))
    ->when($skeleton, fn($collection) => $collection->put('data-skeleton', 'true'));
?>

<div <?php echo e($attributes->map(fn($value, $key) => $key . '="' . e($value) . '"')->implode(' ')); ?>>
    <?php if($skeleton): ?>
        <?php switch($type):
            case ('table'): ?>
                <div class="skeleton-table">
                    <?php for($i = 0; $i < 5; $i++): ?>
                        <div class="skeleton-row">
                            <div class="skeleton-cell"></div>
                            <div class="skeleton-cell"></div>
                            <div class="skeleton-cell"></div>
                            <div class="skeleton-cell"></div>
                        </div>
                    <?php endfor; ?>
                </div>
                <?php break; ?>
                
            <?php case ('card-grid'): ?>
                <div class="skeleton-grid">
                    <?php for($i = 0; $i < 6; $i++): ?>
                        <div class="skeleton-card">
                            <div class="skeleton-image"></div>
                            <div class="skeleton-text"></div>
                            <div class="skeleton-text short"></div>
                        </div>
                    <?php endfor; ?>
                </div>
                <?php break; ?>
                
            <?php case ('text'): ?>
                <div class="skeleton-text-block">
                    <div class="skeleton-text"></div>
                    <div class="skeleton-text medium"></div>
                    <div class="skeleton-text short"></div>
                    <div class="skeleton-text"></div>
                    <div class="skeleton-text medium"></div>
                </div>
                <?php break; ?>
                
            <?php default: ?>
                <div class="loading-overlay">
                    <div class="loading-spinner"></div>
                    <span><?php echo e($loadingText); ?></span>
                </div>
        <?php endswitch; ?>
    <?php else: ?>
        <div class="loading-overlay">
            <div class="loading-spinner"></div>
            <span><?php echo e($loadingText); ?></span>
        </div>
    <?php endif; ?>
    
    <?php if($slot->isNotEmpty()): ?>
        <div class="lazy-content-fallback" style="display: none;">
            <?php echo e($slot); ?>

        </div>
    <?php endif; ?>
</div>
<?php /**PATH C:\xampp\htdocs\pwbapp\resources\views/components/lazy-content.blade.php ENDPATH**/ ?>